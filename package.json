{"name": "financemanagement", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "client": "cd client && npm start", "server": "nodemon server.js", "build": "cd client && npm run build", "install-client": "cd client && npm install", "heroku-postbuild": "npm run install-client && npm run build"}, "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "moment": "^2.29.4", "mongoose": "^7.5.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "joi": "^17.9.2"}, "devDependencies": {"nodemon": "^3.1.10"}}