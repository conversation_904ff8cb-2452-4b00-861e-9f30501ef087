const express = require('express');
const {
  registerUser,
  loginUser,
  getUserProfile,
  updateUserProfile,
  changePassword,
  deleteUser
} = require('../controllers/userController');
const { validateUser } = require('../middleware/validation');
const router = express.Router();

// Routes
router.post('/register', validateUser, registerUser);
router.post('/login', loginUser);
router.get('/profile/:id', getUserProfile);
router.put('/profile/:id', updateUserProfile);
router.put('/change-password/:id', changePassword);
router.delete('/profile/:id', deleteUser);

module.exports = router;