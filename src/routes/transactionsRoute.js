const express = require('express');
const {
  getAllTransactions,
  addTransaction,
  editTransaction,
  deleteTransaction,
  getTransactionStats,
  getTransactionsByCategory
} = require('../controllers/transactionController');
const { validateTransaction } = require('../middleware/validation');
const router = express.Router();


// Routes
router.post('/get-all-transactions', getAllTransactions);
router.post('/add-transaction', validateTransaction, addTransaction);
router.post('/edit-transaction', validateTransaction, editTransaction);
router.post('/delete-transaction', deleteTransaction);
router.post('/get-stats', getTransactionStats);
router.get('/by-category/:category', getTransactionsByCategory);

module.exports = router;;