const express = require('express');
const {
  getAllBudgets,
  addBudget,
  editBudget,
  deleteBudget,
  getBudgetAnalytics
} = require('../controllers/budgetController');
const { validateBudget } = require('../middleware/validation');
const router = express.Router();

// Routes
router.get('/get-all-budgets', getAllBudgets);
router.post('/add-budget', validateBudget, addBudget);
router.post('/edit-budget', validateBudget, editBudget);
router.post('/delete-budget', deleteBudget);
router.get('/analytics', getBudgetAnalytics);

// Get budget status and alerts
router.get('/budget-status/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  const budget = await Budget.findById(id);
  if (!budget) {
    return res.status(404).json({
      success: false,
      error: 'Budget not found'
    });
  }

  const status = {
    id: budget._id,
    name: budget.name,
    progressPercentage: budget.progressPercentage,
    remainingAmount: budget.remainingAmount,
    daysRemaining: budget.daysRemaining,
    isOverThreshold: budget.isOverThreshold(),
    isExceeded: budget.isExceeded(),
    status: budget.status
  };

  res.json({
    success: true,
    data: status
  });
}));

// Get budget analytics
router.get('/budget-analytics', asyncHandler(async (req, res) => {
  const { userid } = req.query;
  
  if (!userid) {
    return res.status(400).json({
      success: false,
      error: 'User ID is required'
    });
  }

  const budgets = await Budget.find({ userid });
  
  const analytics = {
    totalBudgets: budgets.length,
    activeBudgets: budgets.filter(b => b.status === 'active').length,
    exceededBudgets: budgets.filter(b => b.isExceeded()).length,
    completedBudgets: budgets.filter(b => b.status === 'completed').length,
    totalAllocated: budgets.reduce((sum, b) => sum + b.amount, 0),
    totalSpent: budgets.reduce((sum, b) => sum + b.spent, 0),
    averageProgress: budgets.length > 0 
      ? budgets.reduce((sum, b) => sum + b.progressPercentage, 0) / budgets.length 
      : 0,
    budgetsByCategory: budgets.reduce((acc, budget) => {
      acc[budget.category] = (acc[budget.category] || 0) + 1;
      return acc;
    }, {}),
    alertBudgets: budgets.filter(b => b.isOverThreshold() && !b.isExceeded())
  };

  res.json({
    success: true,
    data: analytics
  });
}));

// Update budget spent amount (called when transactions are added/updated)
router.post('/update-spent', asyncHandler(async (req, res) => {
  const { userid, category, startDate, endDate } = req.body;
  
  if (!userid || !category) {
    return res.status(400).json({
      success: false,
      error: 'User ID and category are required'
    });
  }

  // Find budgets for this category
  const budgets = await Budget.find({
    userid,
    category,
    ...(startDate && endDate && {
      $or: [
        { startDate: { $lte: new Date(endDate) }, endDate: { $gte: new Date(startDate) } }
      ]
    })
  });

  for (let budget of budgets) {
    const spent = await Transaction.aggregate([
      {
        $match: {
          userid: userid,
          type: 'expense',
          category: budget.category,
          date: {
            $gte: budget.startDate,
            $lte: budget.endDate
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);
    
    budget.spent = spent.length > 0 ? spent[0].total : 0;
    await budget.save();
  }

  res.json({
    success: true,
    message: 'Budget spent amounts updated successfully'
  });
}));

module.exports = router;
