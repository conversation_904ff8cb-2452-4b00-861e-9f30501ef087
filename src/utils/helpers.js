const moment = require('moment');

/**
 * Calculate comprehensive transaction statistics
 * @param {Array} transactions - Array of transaction objects
 * @returns {Object} Statistics object
 */
const calculateTransactionStats = (transactions) => {
  const stats = {
    totalTransactions: transactions.length,
    totalIncome: 0,
    totalExpense: 0,
    totalBalance: 0,
    incomeTransactions: 0,
    expenseTransactions: 0,
    categoryBreakdown: {},
    monthlyTrend: {},
    averageTransaction: 0,
    largestIncome: 0,
    largestExpense: 0,
    mostFrequentCategory: null,
  };

  if (transactions.length === 0) {
    return stats;
  }

  const categoryCount = {};
  let totalAmount = 0;

  transactions.forEach((transaction) => {
    const amount = Number(transaction.amount);
    const category = transaction.category;
    const month = moment(transaction.date).format('MMM YYYY');

    totalAmount += amount;

    if (transaction.type === 'income') {
      stats.totalIncome += amount;
      stats.incomeTransactions += 1;
      if (amount > stats.largestIncome) {
        stats.largestIncome = amount;
      }
    } else {
      stats.totalExpense += amount;
      stats.expenseTransactions += 1;
      if (amount > stats.largestExpense) {
        stats.largestExpense = amount;
      }
    }

    // Category breakdown
    if (!stats.categoryBreakdown[category]) {
      stats.categoryBreakdown[category] = { income: 0, expense: 0, total: 0, count: 0 };
    }
    stats.categoryBreakdown[category][transaction.type] += amount;
    stats.categoryBreakdown[category].total += amount;
    stats.categoryBreakdown[category].count += 1;

    // Category frequency count
    categoryCount[category] = (categoryCount[category] || 0) + 1;

    // Monthly trend
    if (!stats.monthlyTrend[month]) {
      stats.monthlyTrend[month] = { income: 0, expense: 0, balance: 0, count: 0 };
    }
    if (transaction.type === 'income') {
      stats.monthlyTrend[month].income += amount;
    } else {
      stats.monthlyTrend[month].expense += amount;
    }
    stats.monthlyTrend[month].balance = 
      stats.monthlyTrend[month].income - stats.monthlyTrend[month].expense;
    stats.monthlyTrend[month].count += 1;
  });

  // Calculate derived statistics
  stats.totalBalance = stats.totalIncome - stats.totalExpense;
  stats.incomePercentage = stats.totalTransactions > 0 
    ? (stats.incomeTransactions / stats.totalTransactions) * 100 
    : 0;
  stats.expensePercentage = stats.totalTransactions > 0 
    ? (stats.expenseTransactions / stats.totalTransactions) * 100 
    : 0;
  stats.averageTransaction = stats.totalTransactions > 0 
    ? totalAmount / stats.totalTransactions 
    : 0;

  // Find most frequent category
  const maxCount = Math.max(...Object.values(categoryCount));
  stats.mostFrequentCategory = Object.keys(categoryCount).find(
    category => categoryCount[category] === maxCount
  );

  // Calculate savings rate
  stats.savingsRate = stats.totalIncome > 0 
    ? ((stats.totalIncome - stats.totalExpense) / stats.totalIncome) * 100 
    : 0;

  return stats;
};

/**
 * Format currency amount
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: USD)
 * @returns {string} Formatted currency string
 */
const formatCurrency = (amount, currency = 'USD') => {
  if (amount === null || amount === undefined) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(Number(amount));
};

/**
 * Format percentage
 * @param {number} value - Value to format as percentage
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted percentage string
 */
const formatPercentage = (value, decimals = 1) => {
  if (value === null || value === undefined) return '0%';
  return `${Number(value).toFixed(decimals)}%`;
};

/**
 * Get date range based on frequency
 * @param {string} frequency - Frequency type
 * @param {Array} customRange - Custom date range
 * @returns {Object} Date range object
 */
const getDateRange = (frequency, customRange = null) => {
  if (frequency === 'custom' && customRange) {
    return {
      startDate: moment(customRange[0]).startOf('day').toDate(),
      endDate: moment(customRange[1]).endOf('day').toDate(),
    };
  }

  const days = parseInt(frequency);
  return {
    startDate: moment().subtract(days, 'days').startOf('day').toDate(),
    endDate: moment().endOf('day').toDate(),
  };
};

/**
 * Calculate budget progress percentage
 * @param {number} spent - Amount spent
 * @param {number} budget - Budget amount
 * @returns {number} Progress percentage
 */
const calculateBudgetProgress = (spent, budget) => {
  if (!budget || budget === 0) return 0;
  return Math.min((spent / budget) * 100, 100);
};

/**
 * Get budget status based on spending
 * @param {number} spent - Amount spent
 * @param {number} budget - Budget amount
 * @returns {string} Budget status
 */
const getBudgetStatus = (spent, budget) => {
  const progress = calculateBudgetProgress(spent, budget);
  if (progress >= 100) return 'exceeded';
  if (progress >= 80) return 'warning';
  if (progress >= 60) return 'good';
  return 'excellent';
};

/**
 * Calculate goal progress percentage
 * @param {number} current - Current amount
 * @param {number} target - Target amount
 * @returns {number} Progress percentage
 */
const calculateGoalProgress = (current, target) => {
  if (!target || target === 0) return 0;
  return Math.min((current / target) * 100, 100);
};

/**
 * Generate pagination metadata
 * @param {number} page - Current page
 * @param {number} limit - Items per page
 * @param {number} total - Total items
 * @returns {Object} Pagination metadata
 */
const getPaginationMeta = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    currentPage: page,
    totalPages,
    totalItems: total,
    itemsPerPage: limit,
    hasNextPage,
    hasPrevPage,
    nextPage: hasNextPage ? page + 1 : null,
    prevPage: hasPrevPage ? page - 1 : null,
  };
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} Is valid email
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Generate random string
 * @param {number} length - Length of string
 * @returns {string} Random string
 */
const generateRandomString = (length = 10) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

module.exports = {
  calculateTransactionStats,
  formatCurrency,
  formatPercentage,
  getDateRange,
  calculateBudgetProgress,
  getBudgetStatus,
  calculateGoalProgress,
  getPaginationMeta,
  isValidEmail,
  generateRandomString,
};
