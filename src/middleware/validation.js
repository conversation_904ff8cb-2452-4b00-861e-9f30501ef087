// Validation middleware
const validateTransaction = (req, res, next) => {
  const { amount, type, category, reference, description, date } = req.body;
  const errors = [];

  // Validate amount
  if (!amount || isNaN(amount) || amount <= 0) {
    errors.push('Amount must be a positive number');
  }

  // Validate type
  if (!type || !['income', 'expense'].includes(type)) {
    errors.push('Type must be either "income" or "expense"');
  }

  // Validate category
  if (!category || category.trim().length === 0) {
    errors.push('Category is required');
  }

  // Validate reference
  if (!reference || reference.trim().length === 0) {
    errors.push('Reference is required');
  }

  // Validate description
  if (!description || description.trim().length === 0) {
    errors.push('Description is required');
  }

  // Validate date
  if (!date || isNaN(new Date(date).getTime())) {
    errors.push('Valid date is required');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors
    });
  }

  next();
};

const validateUser = (req, res, next) => {
  const { name, email, password } = req.body;
  const errors = [];

  // Validate name
  if (!name || name.trim().length < 2) {
    errors.push('Name must be at least 2 characters long');
  }

  // Validate email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email || !emailRegex.test(email)) {
    errors.push('Valid email is required');
  }

  // Validate password (only for registration)
  if (req.path === '/register') {
    if (!password || password.length < 6) {
      errors.push('Password must be at least 6 characters long');
    }
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors
    });
  }

  next();
};

const validateBudget = (req, res, next) => {
  const { name, category, amount, period, startDate, endDate } = req.body;
  const errors = [];

  // Validate name
  if (!name || name.trim().length === 0) {
    errors.push('Budget name is required');
  }

  // Validate category
  if (!category || category.trim().length === 0) {
    errors.push('Category is required');
  }

  // Validate amount
  if (!amount || isNaN(amount) || amount <= 0) {
    errors.push('Amount must be a positive number');
  }

  // Validate period
  if (!period || !['weekly', 'monthly', 'quarterly', 'yearly'].includes(period)) {
    errors.push('Period must be weekly, monthly, quarterly, or yearly');
  }

  // Validate dates
  if (!startDate || isNaN(new Date(startDate).getTime())) {
    errors.push('Valid start date is required');
  }

  if (!endDate || isNaN(new Date(endDate).getTime())) {
    errors.push('Valid end date is required');
  }

  if (startDate && endDate && new Date(startDate) >= new Date(endDate)) {
    errors.push('End date must be after start date');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors
    });
  }

  next();
};

const validateGoal = (req, res, next) => {
  const { name, description, type, targetAmount, targetDate } = req.body;
  const errors = [];

  // Validate name
  if (!name || name.trim().length === 0) {
    errors.push('Goal name is required');
  }

  // Validate description
  if (!description || description.trim().length === 0) {
    errors.push('Description is required');
  }

  // Validate type
  const validTypes = ['emergency', 'vacation', 'car', 'home', 'education', 'retirement', 'other'];
  if (!type || !validTypes.includes(type)) {
    errors.push('Valid goal type is required');
  }

  // Validate target amount
  if (!targetAmount || isNaN(targetAmount) || targetAmount <= 0) {
    errors.push('Target amount must be a positive number');
  }

  // Validate target date
  if (!targetDate || isNaN(new Date(targetDate).getTime())) {
    errors.push('Valid target date is required');
  }

  if (targetDate && new Date(targetDate) <= new Date()) {
    errors.push('Target date must be in the future');
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors
    });
  }

  next();
};

// Async wrapper to handle async route errors
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

module.exports = {
  validateTransaction,
  validateUser,
  validateBudget,
  validateGoal,
  asyncHandler
};
