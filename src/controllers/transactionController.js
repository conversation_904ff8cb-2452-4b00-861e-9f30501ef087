const Transaction = require('../models/Transaction');
const { asyncHandler } = require('../middleware/validation');
const { calculateTransactionStats } = require('../utils/helpers');
const moment = require('moment');

// @desc    Get all transactions for a user
// @route   POST /api/transactions/get-all-transactions
// @access  Private
const getAllTransactions = asyncHandler(async (req, res) => {
  const { frequency, selectedRange, type, userid } = req.body;
  
  if (!userid) {
    return res.status(400).json({
      success: false,
      error: 'User ID is required'
    });
  }
  
  const query = { userid };
  
  // Add type filter
  if (type && type !== 'all') {
    query.type = type;
  }
  
  // Add date filter
  if (frequency !== 'custom') {
    query.date = {
      $gt: moment().subtract(Number(frequency), 'd').toDate(),
    };
  } else if (selectedRange && selectedRange.length === 2) {
    query.date = {
      $gte: new Date(selectedRange[0]),
      $lte: new Date(selectedRange[1]),
    };
  }
  
  const transactions = await Transaction.find(query).sort({ date: -1 });
  
  res.json({
    success: true,
    data: transactions,
    count: transactions.length
  });
});

// @desc    Add new transaction
// @route   POST /api/transactions/add-transaction
// @access  Private
const addTransaction = asyncHandler(async (req, res) => {
  const transaction = new Transaction(req.body);
  await transaction.save();
  
  res.status(201).json({
    success: true,
    message: 'Transaction added successfully',
    data: transaction
  });
});

// @desc    Update transaction
// @route   POST /api/transactions/edit-transaction
// @access  Private
const editTransaction = asyncHandler(async (req, res) => {
  const { transactionId, payload } = req.body;
  
  const transaction = await Transaction.findOneAndUpdate(
    { _id: transactionId }, 
    payload, 
    { new: true, runValidators: true }
  );
  
  if (!transaction) {
    return res.status(404).json({
      success: false,
      error: 'Transaction not found'
    });
  }
  
  res.json({
    success: true,
    message: 'Transaction updated successfully',
    data: transaction
  });
});

// @desc    Delete transaction
// @route   POST /api/transactions/delete-transaction
// @access  Private
const deleteTransaction = asyncHandler(async (req, res) => {
  const { transactionId } = req.body;
  
  const transaction = await Transaction.findOneAndDelete({ _id: transactionId });
  
  if (!transaction) {
    return res.status(404).json({
      success: false,
      error: 'Transaction not found'
    });
  }
  
  res.json({
    success: true,
    message: 'Transaction deleted successfully'
  });
});

// @desc    Get transaction statistics
// @route   POST /api/transactions/get-stats
// @access  Private
const getTransactionStats = asyncHandler(async (req, res) => {
  const { userid, frequency, selectedRange, type } = req.body;
  
  if (!userid) {
    return res.status(400).json({
      success: false,
      error: 'User ID is required'
    });
  }
  
  const query = { userid };
  
  // Add filters similar to getAllTransactions
  if (type && type !== 'all') {
    query.type = type;
  }
  
  if (frequency !== 'custom') {
    query.date = {
      $gt: moment().subtract(Number(frequency), 'd').toDate(),
    };
  } else if (selectedRange && selectedRange.length === 2) {
    query.date = {
      $gte: new Date(selectedRange[0]),
      $lte: new Date(selectedRange[1]),
    };
  }
  
  const transactions = await Transaction.find(query);
  const stats = calculateTransactionStats(transactions);
  
  res.json({
    success: true,
    data: stats
  });
});

// @desc    Get transactions by category
// @route   GET /api/transactions/by-category/:category
// @access  Private
const getTransactionsByCategory = asyncHandler(async (req, res) => {
  const { category } = req.params;
  const { userid } = req.query;
  
  if (!userid) {
    return res.status(400).json({
      success: false,
      error: 'User ID is required'
    });
  }
  
  const transactions = await Transaction.find({ 
    userid, 
    category 
  }).sort({ date: -1 });
  
  res.json({
    success: true,
    data: transactions,
    count: transactions.length
  });
});

module.exports = {
  getAllTransactions,
  addTransaction,
  editTransaction,
  deleteTransaction,
  getTransactionStats,
  getTransactionsByCategory
};
