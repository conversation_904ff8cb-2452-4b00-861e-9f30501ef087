const Budget = require('../models/Budget');
const Transaction = require('../models/Transaction');
const { asyncHandler } = require('../middleware/validation');

// @desc    Get all budgets for a user
// @route   GET /api/budgets/get-all-budgets
// @access  Private
const getAllBudgets = asyncHandler(async (req, res) => {
  const { userid } = req.query;
  
  if (!userid) {
    return res.status(400).json({
      success: false,
      error: 'User ID is required'
    });
  }

  const budgets = await Budget.find({ userid }).sort({ createdAt: -1 });
  
  // Calculate spent amounts based on transactions
  for (let budget of budgets) {
    const spent = await Transaction.aggregate([
      {
        $match: {
          userid: userid,
          type: 'expense',
          category: budget.category,
          date: {
            $gte: budget.startDate,
            $lte: budget.endDate
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);
    
    budget.spent = spent.length > 0 ? spent[0].total : 0;
    await budget.save();
  }

  res.json({
    success: true,
    data: budgets
  });
});

// @desc    Add new budget
// @route   POST /api/budgets/add-budget
// @access  Private
const addBudget = asyncHandler(async (req, res) => {
  const budgetData = {
    ...req.body,
    spent: 0,
    status: 'active'
  };

  const budget = new Budget(budgetData);
  await budget.save();

  res.status(201).json({
    success: true,
    message: 'Budget created successfully',
    data: budget
  });
});

// @desc    Update budget
// @route   POST /api/budgets/edit-budget
// @access  Private
const editBudget = asyncHandler(async (req, res) => {
  const { budgetId, payload } = req.body;
  
  const budget = await Budget.findById(budgetId);
  if (!budget) {
    return res.status(404).json({
      success: false,
      error: 'Budget not found'
    });
  }

  Object.assign(budget, payload);
  await budget.save();

  res.json({
    success: true,
    message: 'Budget updated successfully',
    data: budget
  });
});

// @desc    Delete budget
// @route   POST /api/budgets/delete-budget
// @access  Private
const deleteBudget = asyncHandler(async (req, res) => {
  const { budgetId } = req.body;
  
  const budget = await Budget.findById(budgetId);
  if (!budget) {
    return res.status(404).json({
      success: false,
      error: 'Budget not found'
    });
  }

  await Budget.findByIdAndDelete(budgetId);

  res.json({
    success: true,
    message: 'Budget deleted successfully'
  });
});

// @desc    Get budget analytics
// @route   GET /api/budgets/analytics
// @access  Private
const getBudgetAnalytics = asyncHandler(async (req, res) => {
  const { userid } = req.query;
  
  if (!userid) {
    return res.status(400).json({
      success: false,
      error: 'User ID is required'
    });
  }

  const budgets = await Budget.find({ userid });
  
  const analytics = {
    totalBudgets: budgets.length,
    activeBudgets: budgets.filter(b => b.status === 'active').length,
    exceededBudgets: budgets.filter(b => b.isExceeded()).length,
    completedBudgets: budgets.filter(b => b.status === 'completed').length,
    totalAllocated: budgets.reduce((sum, b) => sum + b.amount, 0),
    totalSpent: budgets.reduce((sum, b) => sum + b.spent, 0),
    averageProgress: budgets.length > 0 
      ? budgets.reduce((sum, b) => sum + b.progressPercentage, 0) / budgets.length 
      : 0,
    budgetsByCategory: budgets.reduce((acc, budget) => {
      acc[budget.category] = (acc[budget.category] || 0) + 1;
      return acc;
    }, {}),
    alertBudgets: budgets.filter(b => b.isOverThreshold() && !b.isExceeded())
  };

  res.json({
    success: true,
    data: analytics
  });
});

module.exports = {
  getAllBudgets,
  addBudget,
  editBudget,
  deleteBudget,
  getBudgetAnalytics
};
