# 📈 Enhanced Finance Tracker

A comprehensive personal finance management system built with the MERN stack, featuring advanced analytics, budget management, financial goals tracking, and modern UI design.

## ✨ Key Features

### 🏠 **Enhanced Dashboard**
- **Financial Summary Cards**: Real-time overview of income, expenses, balance, and financial health
- **Recent Transactions**: Quick view of latest financial activities with smart categorization
- **Quick Actions**: One-click access to common tasks like adding transactions, viewing analytics
- **Financial Health Score**: AI-powered assessment of your financial wellness

### 💰 **Smart Transaction Management**
- **Advanced Categorization**: Intelligent expense and income categorization
- **Bulk Operations**: Import/export transactions via CSV
- **Smart Filters**: Filter by date range, category, amount, and transaction type
- **Real-time Validation**: Instant feedback on transaction data

### 📊 **Advanced Analytics & Reporting**
- **Interactive Charts**: Beautiful visualizations using Chart.js
- **Monthly Trends**: Track income vs expense patterns over time
- **Category Breakdown**: Detailed pie charts for spending analysis
- **Custom Reports**: Generate PDF reports with comprehensive financial insights
- **Export Options**: CSV, PDF export with customizable date ranges

### 🎯 **Budget Management**
- **Smart Budget Creation**: Set budgets by category with flexible time periods
- **Real-time Tracking**: Monitor spending against budget limits
- **Alert System**: Get notified when approaching budget limits
- **Budget Analytics**: Detailed performance metrics and recommendations

### 🏆 **Financial Goals Tracking**
- **Goal Categories**: Emergency fund, vacation, car, home, education, retirement
- **Progress Tracking**: Visual progress indicators with milestone celebrations
- **Contribution History**: Track all contributions with detailed logs
- **Smart Recommendations**: AI-powered savings suggestions

### 📱 **Modern UI/UX**
- **Responsive Design**: Seamless experience across desktop, tablet, and mobile
- **Dark/Light Themes**: Customizable interface themes
- **Intuitive Navigation**: Tab-based navigation with breadcrumbs
- **Accessibility**: WCAG compliant design for all users

## 🛠️ Technologies Used

### Frontend
- **React.js 18**: Modern React with hooks and functional components
- **Ant Design**: Professional UI component library
- **Chart.js**: Interactive and responsive charts
- **Axios**: HTTP client for API communication
- **Moment.js**: Date manipulation and formatting
- **React Router**: Client-side routing

### Backend
- **Node.js**: JavaScript runtime environment
- **Express.js**: Web application framework
- **MongoDB**: NoSQL database for flexible data storage
- **Mongoose**: MongoDB object modeling
- **Moment.js**: Server-side date handling

### Additional Libraries
- **jsPDF**: PDF generation for reports
- **html2canvas**: Chart export functionality
- **FontAwesome**: Icon library
- **CSS3**: Modern styling with flexbox and grid

## 📁 Project Structure

```
FinanceTracker/
├── client/                     # React frontend
│   ├── public/
│   ├── src/
│   │   ├── components/         # Reusable components
│   │   │   ├── common/         # Shared components
│   │   │   ├── dashboard/      # Dashboard components
│   │   │   ├── transactions/   # Transaction components
│   │   │   ├── analytics/      # Analytics components
│   │   │   ├── budget/         # Budget management
│   │   │   └── goals/          # Goals tracking
│   │   ├── pages/              # Page components
│   │   ├── hooks/              # Custom React hooks
│   │   ├── services/           # API services
│   │   ├── utils/              # Utility functions
│   │   ├── constants/          # App constants
│   │   └── styles/             # CSS files
│   └── package.json
├── models/                     # MongoDB models
│   ├── User.js
│   ├── Transaction.js
│   ├── Budget.js
│   └── Goal.js
├── routes/                     # Express routes
│   ├── usersRoute.js
│   ├── transactionsRoute.js
│   ├── budgetsRoute.js
│   └── goalsRoute.js
├── middleware/                 # Custom middleware
│   ├── errorHandler.js
│   └── validation.js
├── server.js                   # Express server
├── dbConnect.js               # Database connection
└── package.json
```

## 🚀 Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (local or cloud)
- npm or yarn

### Backend Setup
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd FinanceTracker
   ```

2. **Install backend dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env` file in the root directory:
   ```env
   MONGODB_URI=mongodb://localhost:27017/financetracker
   PORT=5000
   NODE_ENV=development
   ```

4. **Start the backend server**
   ```bash
   npm start
   # or for development with nodemon
   npm run dev
   ```

### Frontend Setup
1. **Navigate to client directory**
   ```bash
   cd client
   ```

2. **Install frontend dependencies**
   ```bash
   npm install
   ```

3. **Start the React development server**
   ```bash
   npm start
   ```

4. **Access the application**
   Open [http://localhost:3000](http://localhost:3000) in your browser

## 📊 API Endpoints

### Authentication
- `POST /api/users/register` - User registration
- `POST /api/users/login` - User login

### Transactions
- `POST /api/transactions/get-all-transactions` - Get filtered transactions
- `POST /api/transactions/add-transaction` - Add new transaction
- `POST /api/transactions/edit-transaction` - Update transaction
- `POST /api/transactions/delete-transaction` - Delete transaction

### Budgets
- `GET /api/budgets/get-all-budgets` - Get user budgets
- `POST /api/budgets/add-budget` - Create new budget
- `POST /api/budgets/edit-budget` - Update budget
- `POST /api/budgets/delete-budget` - Delete budget
- `GET /api/budgets/budget-analytics` - Get budget analytics

### Goals
- `GET /api/goals/get-all-goals` - Get user goals
- `POST /api/goals/add-goal` - Create new goal
- `POST /api/goals/edit-goal` - Update goal
- `POST /api/goals/delete-goal` - Delete goal
- `POST /api/goals/update-progress` - Add contribution to goal

## 🎨 UI Components

### Dashboard Components
- **DashboardSummary**: Financial overview cards
- **RecentTransactions**: Latest transaction list
- **QuickActions**: Action buttons for common tasks

### Analytics Components
- **Analytics**: Comprehensive financial analytics with charts
- **Charts**: Interactive visualizations for data insights

### Budget & Goals
- **BudgetManager**: Budget creation and tracking
- **GoalsManager**: Financial goals management

## 🔧 Configuration

### Environment Variables
```env
# Database
MONGODB_URI=mongodb://localhost:27017/financetracker

# Server
PORT=5000
NODE_ENV=development

# Optional: JWT Secret (for future authentication enhancement)
JWT_SECRET=your_jwt_secret_here
```

### Database Configuration
The application uses MongoDB with Mongoose ODM. Models include:
- **User**: User authentication and profile data
- **Transaction**: Financial transaction records
- **Budget**: Budget planning and tracking
- **Goal**: Financial goals and progress

## 🚀 Deployment

### Backend Deployment (Heroku/Railway)
1. Set environment variables in your hosting platform
2. Ensure MongoDB connection string is configured
3. Deploy using Git or platform-specific CLI

### Frontend Deployment (Netlify/Vercel)
1. Build the React app: `npm run build`
2. Deploy the `build` folder to your hosting platform
3. Configure API proxy settings if needed

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Ant Design team for the excellent UI components
- Chart.js community for visualization tools
- MongoDB team for the flexible database solution
- React team for the amazing frontend framework

## 📞 Support

For support, email <EMAIL> or create an issue in the repository.

---

**Built with ❤️ using the MERN Stack**
