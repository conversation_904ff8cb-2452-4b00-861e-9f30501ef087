import React, { useState } from 'react';
import {
  Mo<PERSON>,
  But<PERSON>,
  Card,
  Row,
  Col,
  Select,
  DatePicker,
  message,
  Spin,
  Typography,
  Space,
  Divider
} from 'antd';
import {
  DownloadOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileExcelOutlined,
  BarChartOutlined,
  CalendarOutlined,
  FilterOutlined
} from '@ant-design/icons';
import {
  exportToCSV,
  exportToPDF,
  exportBudgetReport,
  exportGoalsReport,
  exportChartAsImage
} from '../../utils/exportUtils';
import { calculateTransactionStats } from '../../utils/helpers';
import moment from 'moment';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

const ExportModal = ({ 
  visible, 
  onCancel, 
  transactions, 
  budgets = [], 
  goals = [] 
}) => {
  const [loading, setLoading] = useState(false);
  const [exportType, setExportType] = useState('transactions');
  const [format, setFormat] = useState('csv');
  const [dateRange, setDateRange] = useState([]);
  const [reportType, setReportType] = useState('summary');

  const handleExport = async () => {
    try {
      setLoading(true);
      
      // Filter transactions by date range if specified
      let filteredTransactions = transactions;
      if (dateRange.length === 2) {
        const startDate = moment(dateRange[0]).startOf('day');
        const endDate = moment(dateRange[1]).endOf('day');
        filteredTransactions = transactions.filter(transaction => {
          const transactionDate = moment(transaction.date);
          return transactionDate.isBetween(startDate, endDate, 'day', '[]');
        });
      }

      switch (exportType) {
        case 'transactions':
          if (format === 'csv') {
            exportToCSV(filteredTransactions, 'transactions');
            message.success('Transactions exported to CSV successfully!');
          } else if (format === 'pdf') {
            const stats = calculateTransactionStats(filteredTransactions);
            await exportToPDF(filteredTransactions, stats, 'transactions');
            message.success('Transactions exported to PDF successfully!');
          }
          break;

        case 'financial-report':
          const stats = calculateTransactionStats(filteredTransactions);
          await exportToPDF(filteredTransactions, stats, reportType);
          message.success('Financial report exported successfully!');
          break;

        case 'budget-report':
          if (budgets.length === 0) {
            message.warning('No budgets available to export');
            return;
          }
          exportBudgetReport(budgets, filteredTransactions);
          message.success('Budget report exported successfully!');
          break;

        case 'goals-report':
          if (goals.length === 0) {
            message.warning('No goals available to export');
            return;
          }
          exportGoalsReport(goals);
          message.success('Goals report exported successfully!');
          break;

        case 'chart':
          // This would need to be implemented with specific chart IDs
          message.info('Chart export functionality coming soon!');
          break;

        default:
          message.error('Invalid export type selected');
      }

      onCancel();
    } catch (error) {
      message.error('Export failed: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const exportOptions = [
    {
      key: 'transactions',
      title: 'Transaction Data',
      description: 'Export your transaction history',
      icon: <FileExcelOutlined style={{ fontSize: '24px', color: '#52c41a' }} />,
      formats: ['csv', 'pdf']
    },
    {
      key: 'financial-report',
      title: 'Financial Report',
      description: 'Comprehensive financial analysis',
      icon: <FileTextOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      formats: ['pdf']
    },
    {
      key: 'budget-report',
      title: 'Budget Report',
      description: 'Budget performance and analysis',
      icon: <FilePdfOutlined style={{ fontSize: '24px', color: '#722ed1' }} />,
      formats: ['pdf'],
      disabled: budgets.length === 0
    },
    {
      key: 'goals-report',
      title: 'Goals Report',
      description: 'Financial goals progress report',
      icon: <BarChartOutlined style={{ fontSize: '24px', color: '#faad14' }} />,
      formats: ['pdf'],
      disabled: goals.length === 0
    }
  ];

  const getFormatIcon = (formatType) => {
    switch (formatType) {
      case 'csv':
        return <FileExcelOutlined style={{ color: '#52c41a' }} />;
      case 'pdf':
        return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <FileTextOutlined />;
    }
  };

  const getFormatLabel = (formatType) => {
    switch (formatType) {
      case 'csv':
        return 'CSV (Excel Compatible)';
      case 'pdf':
        return 'PDF Document';
      default:
        return formatType.toUpperCase();
    }
  };

  return (
    <Modal
      title={
        <Space>
          <DownloadOutlined />
          Export Data
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button
          key="export"
          type="primary"
          icon={<DownloadOutlined />}
          onClick={handleExport}
          loading={loading}
          disabled={!exportType}
        >
          Export
        </Button>
      ]}
    >
      <Spin spinning={loading}>
        <div style={{ marginBottom: '24px' }}>
          <Title level={5}>
            <FilterOutlined style={{ marginRight: '8px' }} />
            Select Export Type
          </Title>
          <Row gutter={[16, 16]}>
            {exportOptions.map((option) => (
              <Col xs={24} sm={12} key={option.key}>
                <Card
                  hoverable
                  className={`export-option-card ${exportType === option.key ? 'selected' : ''} ${option.disabled ? 'disabled' : ''}`}
                  style={{
                    border: exportType === option.key ? '2px solid #1890ff' : '1px solid #d9d9d9',
                    opacity: option.disabled ? 0.5 : 1,
                    cursor: option.disabled ? 'not-allowed' : 'pointer'
                  }}
                  onClick={() => !option.disabled && setExportType(option.key)}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    {option.icon}
                    <div>
                      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                        {option.title}
                      </div>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        {option.description}
                      </div>
                      {option.disabled && (
                        <div style={{ color: '#ff4d4f', fontSize: '11px', marginTop: '4px' }}>
                          No data available
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>

        {exportType && (
          <>
            <Divider />
            
            {/* Format Selection */}
            <div style={{ marginBottom: '16px' }}>
              <Title level={5}>Export Format</Title>
              <Select
                value={format}
                onChange={setFormat}
                style={{ width: '100%' }}
                placeholder="Select export format"
              >
                {exportOptions
                  .find(opt => opt.key === exportType)
                  ?.formats.map(formatType => (
                    <Option key={formatType} value={formatType}>
                      <Space>
                        {getFormatIcon(formatType)}
                        {getFormatLabel(formatType)}
                      </Space>
                    </Option>
                  ))}
              </Select>
            </div>

            {/* Report Type Selection for Financial Reports */}
            {exportType === 'financial-report' && (
              <div style={{ marginBottom: '16px' }}>
                <Title level={5}>Report Type</Title>
                <Select
                  value={reportType}
                  onChange={setReportType}
                  style={{ width: '100%' }}
                  placeholder="Select report type"
                >
                  <Option value="summary">Summary Report</Option>
                  <Option value="detailed">Detailed Report</Option>
                  <Option value="transactions">Transactions Only</Option>
                </Select>
              </div>
            )}

            {/* Date Range Selection */}
            {(exportType === 'transactions' || exportType === 'financial-report') && (
              <div style={{ marginBottom: '16px' }}>
                <Title level={5}>
                  <CalendarOutlined style={{ marginRight: '8px' }} />
                  Date Range (Optional)
                </Title>
                <RangePicker
                  value={dateRange}
                  onChange={setDateRange}
                  style={{ width: '100%' }}
                  placeholder={['Start Date', 'End Date']}
                />
                <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginTop: '4px' }}>
                  Leave empty to export all data
                </Text>
              </div>
            )}

            {/* Export Preview */}
            <div style={{ 
              background: '#f5f5f5', 
              padding: '16px', 
              borderRadius: '6px',
              marginTop: '16px'
            }}>
              <Title level={5} style={{ margin: 0, marginBottom: '8px' }}>
                Export Preview
              </Title>
              <Text type="secondary">
                {exportType === 'transactions' && (
                  <>
                    Exporting {dateRange.length === 2 ? 'filtered' : 'all'} transaction data 
                    ({transactions.length} transactions) as {format.toUpperCase()}
                  </>
                )}
                {exportType === 'financial-report' && (
                  <>
                    Generating {reportType} financial report as PDF
                    {dateRange.length === 2 && ' for selected date range'}
                  </>
                )}
                {exportType === 'budget-report' && (
                  <>
                    Exporting budget performance report ({budgets.length} budgets) as PDF
                  </>
                )}
                {exportType === 'goals-report' && (
                  <>
                    Exporting financial goals report ({goals.length} goals) as PDF
                  </>
                )}
              </Text>
            </div>
          </>
        )}
      </Spin>
    </Modal>
  );
};

export default ExportModal;
