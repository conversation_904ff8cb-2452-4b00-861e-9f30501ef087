# Server Configuration
PORT=5000
HOST=localhost
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/financetracker

# JWT Configuration (for future authentication enhancement)
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Session Configuration
SESSION_SECRET=your-session-secret-here

# Email Configuration (for future features)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif

# Security
BCRYPT_ROUNDS=12

# API Configuration
API_PREFIX=/api
API_VERSION=v1
