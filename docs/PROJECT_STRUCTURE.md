# Finance Tracker - Project Structure

## 📁 Root Directory Structure

```
FinanceTracker/
├── 📁 src/                          # Backend source code
│   ├── 📁 config/                   # Configuration files
│   │   ├── database.js              # Database connection
│   │   └── index.js                 # Main configuration
│   ├── 📁 controllers/              # Route controllers
│   │   ├── budgetController.js      # Budget management logic
│   │   ├── transactionController.js # Transaction management logic
│   │   └── userController.js        # User management logic
│   ├── 📁 middleware/               # Custom middleware
│   │   ├── errorHandler.js          # Global error handling
│   │   └── validation.js            # Input validation
│   ├── 📁 models/                   # Database models
│   │   ├── Budget.js                # Budget schema
│   │   ├── Goal.js                  # Goal schema
│   │   ├── Transaction.js           # Transaction schema
│   │   └── User.js                  # User schema
│   ├── 📁 routes/                   # API routes
│   │   ├── budgetsRoute.js          # Budget endpoints
│   │   ├── goalsRoute.js            # Goals endpoints
│   │   ├── transactionsRoute.js     # Transaction endpoints
│   │   └── usersRoute.js            # User endpoints
│   ├── 📁 services/                 # Business logic services
│   ├── 📁 utils/                    # Utility functions
│   │   └── helpers.js               # Common helper functions
│   └── app.js                       # Express app configuration
├── 📁 client/                       # Frontend React application
│   ├── 📁 public/                   # Static assets
│   └── 📁 src/                      # React source code
│       ├── 📁 components/           # React components
│       │   ├── 📁 analytics/        # Analytics components
│       │   ├── 📁 budget/           # Budget management components
│       │   ├── 📁 charts/           # Chart components
│       │   ├── 📁 common/           # Shared components
│       │   ├── 📁 dashboard/        # Dashboard components
│       │   ├── 📁 forms/            # Form components
│       │   ├── 📁 goals/            # Goals components
│       │   ├── 📁 layout/           # Layout components
│       │   ├── 📁 transactions/     # Transaction components
│       │   └── 📁 ui/               # UI components
│       ├── 📁 constants/            # Application constants
│       ├── 📁 context/              # React context providers
│       ├── 📁 hooks/                # Custom React hooks
│       ├── 📁 pages/                # Page components
│       ├── 📁 services/             # API service functions
│       ├── 📁 styles/               # CSS and styling files
│       ├── 📁 types/                # TypeScript type definitions
│       └── 📁 utils/                # Frontend utility functions
├── 📁 docs/                         # Project documentation
│   ├── 📁 api/                      # API documentation
│   ├── 📁 deployment/               # Deployment guides
│   ├── 📁 development/              # Development guides
│   └── 📁 frontend/                 # Frontend documentation
├── 📁 scripts/                      # Build and deployment scripts
├── 📁 tests/                        # Test files
├── 📄 .env.example                  # Environment variables template
├── 📄 .gitignore                    # Git ignore rules
├── 📄 package.json                  # Backend dependencies
├── 📄 README.md                     # Project overview
└── 📄 server.js                     # Server entry point
```

## 🏗️ Architecture Overview

### Backend Architecture (MVC Pattern)

```
Request → Routes → Controllers → Services → Models → Database
                ↓
            Middleware (Validation, Error Handling)
```

**Components:**
- **Routes**: Define API endpoints and HTTP methods
- **Controllers**: Handle request/response logic
- **Services**: Business logic and data processing
- **Models**: Database schema and data validation
- **Middleware**: Cross-cutting concerns (auth, validation, errors)

### Frontend Architecture (Component-Based)

```
App → Pages → Components → Hooks/Services → API
```

**Components:**
- **Pages**: Top-level route components
- **Components**: Reusable UI components organized by feature
- **Hooks**: Custom React hooks for state management
- **Services**: API communication layer
- **Utils**: Helper functions and utilities

## 📂 Detailed Directory Breakdown

### Backend (`/src`)

#### Controllers (`/src/controllers`)
- Handle HTTP requests and responses
- Validate input data
- Call appropriate services
- Return formatted responses

#### Models (`/src/models`)
- Define database schemas using Mongoose
- Include validation rules and virtual properties
- Define instance and static methods

#### Routes (`/src/routes`)
- Define API endpoints
- Apply middleware (validation, authentication)
- Route requests to appropriate controllers

#### Middleware (`/src/middleware`)
- **errorHandler.js**: Global error handling and formatting
- **validation.js**: Input validation for all endpoints

#### Utils (`/src/utils`)
- **helpers.js**: Common utility functions for calculations, formatting, etc.

### Frontend (`/client/src`)

#### Components (`/client/src/components`)
- **analytics/**: Charts, reports, and data visualization
- **budget/**: Budget creation, editing, and tracking
- **charts/**: Reusable chart components
- **common/**: Shared components used across features
- **dashboard/**: Dashboard summary, quick actions, recent items
- **forms/**: Form components and input elements
- **goals/**: Financial goals management
- **layout/**: Page layout and navigation components
- **transactions/**: Transaction CRUD operations
- **ui/**: Basic UI components (buttons, modals, etc.)

#### Services (`/client/src/services`)
- **api.js**: Centralized API communication
- Feature-specific service files for complex operations

#### Hooks (`/client/src/hooks`)
- **useTransactions.js**: Transaction state management
- Custom hooks for other features

#### Utils (`/client/src/utils`)
- **helpers.js**: Frontend utility functions
- **exportUtils.js**: Data export functionality

## 🔄 Data Flow

### Transaction Creation Flow
```
User Input → Form Validation → API Call → Controller → Service → Model → Database
                                                                    ↓
User Interface ← Component Update ← State Update ← API Response ← Controller
```

### Dashboard Data Flow
```
Page Load → Multiple API Calls → Controllers → Services → Database Queries
                                                              ↓
Dashboard Components ← Processed Data ← API Responses ← Controllers
```

## 🛡️ Security Layers

1. **Input Validation**: All inputs validated at route level
2. **Error Handling**: Centralized error processing
3. **Rate Limiting**: API request throttling
4. **CORS**: Cross-origin request security
5. **Helmet**: Security headers

## 📊 Database Design

### Collections
- **users**: User accounts and profiles
- **transactions**: Financial transactions
- **budgets**: Budget planning and tracking
- **goals**: Financial goals and progress

### Relationships
- Users → Transactions (1:many)
- Users → Budgets (1:many)
- Users → Goals (1:many)

## 🚀 Deployment Structure

### Development
- Backend: `http://localhost:5000`
- Frontend: `http://localhost:3000`
- Database: Local MongoDB

### Production
- Backend: Deployed on cloud platform (Heroku, Railway, etc.)
- Frontend: Deployed on CDN (Netlify, Vercel, etc.)
- Database: Cloud MongoDB (Atlas)

## 📝 Naming Conventions

### Files and Directories
- **camelCase**: JavaScript files and variables
- **PascalCase**: React components
- **kebab-case**: CSS classes and file names
- **UPPER_CASE**: Constants and environment variables

### API Endpoints
- **RESTful**: `/api/resource/action`
- **Consistent**: Use standard HTTP methods
- **Versioned**: Include version in URL when needed

This structure ensures:
- ✅ **Scalability**: Easy to add new features
- ✅ **Maintainability**: Clear separation of concerns
- ✅ **Testability**: Isolated components and functions
- ✅ **Reusability**: Modular and composable architecture
