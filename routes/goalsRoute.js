const express = require('express');
const Goal = require('../models/Goal');
const { validateGoal, asyncHandler } = require('../middleware/validation');
const router = express.Router();

// Get all goals for a user
router.get('/get-all-goals', asyncHandler(async (req, res) => {
  const { userid } = req.query;
  
  if (!userid) {
    return res.status(400).json({
      success: false,
      error: 'User ID is required'
    });
  }

  const goals = await Goal.find({ userid }).sort({ createdAt: -1 });

  res.json({
    success: true,
    data: goals
  });
}));

// Add new goal
router.post('/add-goal', validateGoal, asyncHandler(async (req, res) => {
  const goalData = {
    ...req.body,
    currentAmount: 0,
    status: 'active'
  };

  const goal = new Goal(goalData);
  await goal.save();

  res.status(201).json({
    success: true,
    message: 'Goal created successfully',
    data: goal
  });
}));

// Edit goal
router.post('/edit-goal', validateGoal, asyncHandler(async (req, res) => {
  const { goalId, payload } = req.body;
  
  const goal = await Goal.findById(goalId);
  if (!goal) {
    return res.status(404).json({
      success: false,
      error: 'Goal not found'
    });
  }

  // Update goal
  Object.assign(goal, payload);
  await goal.save();

  res.json({
    success: true,
    message: 'Goal updated successfully',
    data: goal
  });
}));

// Delete goal
router.post('/delete-goal', asyncHandler(async (req, res) => {
  const { goalId } = req.body;
  
  const goal = await Goal.findById(goalId);
  if (!goal) {
    return res.status(404).json({
      success: false,
      error: 'Goal not found'
    });
  }

  await Goal.findByIdAndDelete(goalId);

  res.json({
    success: true,
    message: 'Goal deleted successfully'
  });
}));

// Update goal progress (add contribution)
router.post('/update-progress', asyncHandler(async (req, res) => {
  const { goalId, amount, note } = req.body;
  
  if (!goalId || !amount || amount <= 0) {
    return res.status(400).json({
      success: false,
      error: 'Goal ID and positive amount are required'
    });
  }

  const goal = await Goal.findById(goalId);
  if (!goal) {
    return res.status(404).json({
      success: false,
      error: 'Goal not found'
    });
  }

  // Add contribution
  await goal.addContribution(amount, note || '');

  res.json({
    success: true,
    message: 'Contribution added successfully',
    data: goal
  });
}));

// Get goal analytics
router.get('/goal-analytics', asyncHandler(async (req, res) => {
  const { userid } = req.query;
  
  if (!userid) {
    return res.status(400).json({
      success: false,
      error: 'User ID is required'
    });
  }

  const goals = await Goal.find({ userid });
  
  const analytics = {
    totalGoals: goals.length,
    activeGoals: goals.filter(g => g.status === 'active').length,
    completedGoals: goals.filter(g => g.status === 'completed').length,
    pausedGoals: goals.filter(g => g.status === 'paused').length,
    totalTargetAmount: goals.reduce((sum, g) => sum + g.targetAmount, 0),
    totalCurrentAmount: goals.reduce((sum, g) => sum + g.currentAmount, 0),
    totalRemainingAmount: goals.reduce((sum, g) => sum + g.remainingAmount, 0),
    averageProgress: goals.length > 0 
      ? goals.reduce((sum, g) => sum + g.progressPercentage, 0) / goals.length 
      : 0,
    goalsByType: goals.reduce((acc, goal) => {
      acc[goal.type] = (acc[goal.type] || 0) + 1;
      return acc;
    }, {}),
    goalsByStatus: goals.reduce((acc, goal) => {
      acc[goal.status] = (acc[goal.status] || 0) + 1;
      return acc;
    }, {}),
    overdueGoals: goals.filter(g => g.isOverdue()),
    nearCompletionGoals: goals.filter(g => g.progressPercentage >= 80 && g.status === 'active'),
    monthlyContributions: goals.reduce((sum, g) => {
      const recentContributions = g.contributions.filter(c => {
        const contributionDate = new Date(c.date);
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return contributionDate >= thirtyDaysAgo;
      });
      return sum + recentContributions.reduce((contribSum, c) => contribSum + c.amount, 0);
    }, 0)
  };

  res.json({
    success: true,
    data: analytics
  });
}));

// Get goal milestones
router.get('/goal-milestones/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  const goal = await Goal.findById(id);
  if (!goal) {
    return res.status(404).json({
      success: false,
      error: 'Goal not found'
    });
  }

  res.json({
    success: true,
    data: {
      goalId: goal._id,
      goalName: goal.name,
      milestones: goal.milestones,
      currentAmount: goal.currentAmount,
      targetAmount: goal.targetAmount,
      progressPercentage: goal.progressPercentage
    }
  });
}));

// Add milestone to goal
router.post('/add-milestone', asyncHandler(async (req, res) => {
  const { goalId, amount, description } = req.body;
  
  if (!goalId || !amount || !description) {
    return res.status(400).json({
      success: false,
      error: 'Goal ID, amount, and description are required'
    });
  }

  const goal = await Goal.findById(goalId);
  if (!goal) {
    return res.status(404).json({
      success: false,
      error: 'Goal not found'
    });
  }

  // Add milestone
  goal.milestones.push({
    amount: amount,
    description: description,
    achieved: goal.currentAmount >= amount,
    achievedDate: goal.currentAmount >= amount ? new Date() : null
  });

  // Sort milestones by amount
  goal.milestones.sort((a, b) => a.amount - b.amount);

  await goal.save();

  res.json({
    success: true,
    message: 'Milestone added successfully',
    data: goal
  });
}));

// Get goal contribution history
router.get('/contribution-history/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { limit = 10, page = 1 } = req.query;
  
  const goal = await Goal.findById(id);
  if (!goal) {
    return res.status(404).json({
      success: false,
      error: 'Goal not found'
    });
  }

  // Sort contributions by date (newest first)
  const sortedContributions = goal.contributions.sort((a, b) => new Date(b.date) - new Date(a.date));
  
  // Paginate
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedContributions = sortedContributions.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: {
      goalId: goal._id,
      goalName: goal.name,
      contributions: paginatedContributions,
      totalContributions: goal.contributions.length,
      currentPage: parseInt(page),
      totalPages: Math.ceil(goal.contributions.length / limit),
      totalAmount: goal.contributions.reduce((sum, c) => sum + c.amount, 0)
    }
  });
}));

module.exports = router;
