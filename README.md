# 💰 Finance Tracker

A clean, professional personal finance management application built with React and Node.js.

## ✨ Features

- **👤 User Management**: Simple registration and login
- **💳 Transaction Tracking**: Add, edit, delete, and categorize transactions
- **📊 Dashboard**: Financial overview with summary cards and recent transactions
- **💰 Budget Management**: Create and track spending budgets by category
- **🎯 Financial Goals**: Set savings goals and track progress
- **📈 Analytics**: Visual charts and financial insights
- **📄 Export**: Download transaction data as CSV or PDF

## 🛠️ Tech Stack

### Frontend
- **React.js** - User interface
- **CSS3** - Modern styling and responsive design
- **Chart.js** - Data visualization
- **Moment.js** - Date handling

### Backend
- **Node.js** - Server runtime
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM for MongoDB

## 🚀 Quick Start

### Prerequisites
- Node.js (v14+)
- MongoDB
- npm

### Installation

1. **Clone and install dependencies**
```bash
git clone <repository-url>
cd FinanceTracker-main

# Install backend dependencies
npm install

# Install frontend dependencies
cd client && npm install && cd ..
```

2. **Start MongoDB**
```bash
# Make sure MongoDB is running on localhost:27017
mongod
```

3. **Run the application**
```bash
# Start backend server (port 5000)
npm run dev

# In another terminal, start frontend (port 3000)
npm run client
```

4. **Access the application**
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- Health Check: http://localhost:5000/api/health

## 📁 Clean Project Structure

```
FinanceTracker/
├── 📄 server.js                    # Server entry point
├── 📄 package.json                 # Backend dependencies
│
├── 📁 src/                         # Backend source
│   ├── 📁 config/                  # Configuration
│   │   └── 📄 database.js          # MongoDB connection
│   ├── 📁 models/                  # Database schemas
│   │   ├── 📄 User.js              # User model
│   │   ├── 📄 Transaction.js       # Transaction model
│   │   ├── 📄 Budget.js            # Budget model
│   │   └── 📄 Goal.js              # Goal model
│   └── 📁 routes/                  # API endpoints
│       ├── 📄 usersRoute.js        # User authentication
│       ├── 📄 transactionsRoute.js # Transaction management
│       ├── 📄 budgetsRoute.js      # Budget operations
│       └── 📄 goalsRoute.js        # Goal tracking
│
└── 📁 client/                      # Frontend React app
    ├── 📄 package.json             # Frontend dependencies
    └── 📁 src/
        ├── 📁 components/          # React components
        │   ├── 📁 analytics/       # Analytics & charts
        │   ├── 📁 budget/          # Budget management
        │   ├── 📁 dashboard/       # Dashboard components
        │   ├── 📁 goals/           # Goals management
        │   ├── 📁 layout/          # App layout
        │   ├── 📁 transactions/    # Transaction forms
        │   └── 📁 ui/              # UI components
        ├── 📁 pages/               # Page components
        ├── 📁 styles/              # CSS files
        └── 📁 hooks/               # Custom React hooks
```

## 🔌 API Endpoints

### 👤 Users
```
POST /api/users/register    # Register new user
POST /api/users/login       # User login
```

### 💳 Transactions
```
POST /api/transactions/get-all-transactions  # Get transactions with filters
POST /api/transactions/add-transaction       # Add new transaction
POST /api/transactions/edit-transaction      # Update transaction
POST /api/transactions/delete-transaction    # Delete transaction
```

### 💰 Budgets
```
GET  /api/budgets/get-all-budgets    # Get user budgets
POST /api/budgets/add-budget         # Create budget
POST /api/budgets/edit-budget        # Update budget
POST /api/budgets/delete-budget      # Delete budget
```

### 🎯 Goals
```
GET  /api/goals/get-all-goals        # Get user goals
POST /api/goals/add-goal             # Create goal
POST /api/goals/edit-goal            # Update goal
POST /api/goals/delete-goal          # Delete goal
POST /api/goals/update-progress      # Add contribution
```

## 🎯 Key Improvements Made

✅ **Clean Architecture**: Organized folder structure with clear separation of concerns  
✅ **Simplified Code**: Removed unnecessary complexity and dependencies  
✅ **Professional Structure**: Industry-standard project organization  
✅ **Working Features**: All core functionality properly implemented  
✅ **Easy Maintenance**: Clear, readable code with consistent patterns  
✅ **Scalable Design**: Easy to add new features and components  

## 🚀 Development Scripts

```bash
# Backend
npm start          # Start production server
npm run dev        # Start development server with nodemon

# Frontend
npm run client     # Start React development server
npm run build      # Build for production

# Full stack
npm run dev        # Start backend
npm run client     # Start frontend (in separate terminal)
```

## 📝 Usage

1. **Register/Login** to create your account
2. **Add Transactions** to track income and expenses
3. **Create Budgets** to manage spending by category
4. **Set Goals** to save for specific objectives
5. **View Analytics** to understand your financial patterns
6. **Export Data** for external analysis

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

---

**Built with ❤️ for better financial management**
