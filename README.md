# Online Finance Management System

This project is an Online Finance Management System developed using the MERN (MongoDB, Express.js, React.js, Node.js) stack. It is designed to assist users in managing their finances efficiently by providing various tools and features to track, analyze, and visualize financial data.

![Finance-Management (4)](https://github.com/diaskalana/Finance-Management-System/assets/74653324/13f05091-4657-475c-b8ff-76a1a08d6ad2)

## Features

- **User Authentication**: Secure user authentication and authorization system.
- **Dashboard**: An interactive dashboard displaying a comprehensive financial summary.
- **Expense Tracker**: Allows users to log and categorize expenses.
- **Income Management**: Tracks and manages various sources of income.
- **Budget Planning**: Helps in planning and setting budgets for different categories.
- **Visual Analytics**: Provides graphical representations of financial data for easy understanding.
- **Responsive Design**: Ensures a seamless experience across devices.

![Finance-Management (5)](https://github.com/diaskalana/Finance-Management-System/assets/74653324/685d0b75-599b-4125-8daf-00b8e2380218)
![Finance-Management (6)](https://github.com/diaskalana/Finance-Management-System/assets/74653324/07f7f71f-3ba7-4770-aab4-d4bd727bd9e1)
![Finance-Management (7)](https://github.com/diaskalana/Finance-Management-System/assets/74653324/9caf42ee-fb76-4589-959b-912d11fe9c8f)

## Technologies Used

- **MongoDB**: NoSQL database used for storing financial data.
- **Express.js**: Backend framework for building robust APIs.
- **React.js**: Frontend library for creating interactive user interfaces.
- **Node.js**: JavaScript runtime for server-side logic.

## Installation

1. Clone the repository.
2. Navigate to the project directory.
3. Install dependencies using `npm install` in both the client and server directories.
4. Configure the environment variables.
5. Run the application using `npm start` for both frontend and backend.

## Usage

1. Register for an account or log in if you already have one.
2. Explore the dashboard to view the financial summary.
3. Add expenses, manage income, and plan budgets accordingly.
4. Analyze graphical representations of financial data for insights.

## Contributing

Contributions are welcome! Feel free to submit issues and pull requests.
