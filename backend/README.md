# 🔧 Finance Tracker - Backend

Node.js/Express backend API for the Finance Tracker application.

## 🚀 Quick Start

### Prerequisites
- Node.js (v14+)
- MongoDB running on localhost:27017

### Installation & Run
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Start production server
npm start
```

The backend will run on **http://localhost:5000**

## 📁 Project Structure

```
backend/
├── 📄 server.js                    # Server entry point
├── 📄 package.json                 # Dependencies & scripts
│
└── 📁 src/
    ├── 📁 config/
    │   └── 📄 database.js          # MongoDB connection
    ├── 📁 models/                  # Database schemas
    │   ├── 📄 User.js              # User model
    │   ├── 📄 Transaction.js       # Transaction model
    │   ├── 📄 Budget.js            # Budget model
    │   └── 📄 Goal.js              # Goal model
    └── 📁 routes/                  # API endpoints
        ├── 📄 usersRoute.js        # User authentication
        ├── 📄 transactionsRoute.js # Transaction management
        ├── 📄 budgetsRoute.js      # Budget operations
        └── 📄 goalsRoute.js        # Goal tracking
```

## 🔌 API Endpoints

### Health Check
- `GET /api/health` - Server health status

### Users
- `POST /api/users/register` - Register new user
- `POST /api/users/login` - User login

### Transactions
- `POST /api/transactions/get-all-transactions` - Get transactions with filters
- `POST /api/transactions/add-transaction` - Add new transaction
- `POST /api/transactions/edit-transaction` - Update transaction
- `POST /api/transactions/delete-transaction` - Delete transaction

### Budgets
- `GET /api/budgets/get-all-budgets` - Get user budgets
- `POST /api/budgets/add-budget` - Create budget
- `POST /api/budgets/edit-budget` - Update budget
- `POST /api/budgets/delete-budget` - Delete budget

### Goals
- `GET /api/goals/get-all-goals` - Get user goals
- `POST /api/goals/add-goal` - Create goal
- `POST /api/goals/edit-goal` - Update goal
- `POST /api/goals/delete-goal` - Delete goal
- `POST /api/goals/update-progress` - Add contribution

## 🛠️ Tech Stack

- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM for MongoDB
- **Moment.js** - Date handling

## 📊 Database Models

### User
- Basic user information and authentication

### Transaction
- Financial transactions with categories and amounts
- Support for income and expense types

### Budget
- Budget planning with spending limits
- Category-based budget tracking
- Progress monitoring with virtual fields

### Goal
- Financial goals with target amounts
- Progress tracking and contributions
- Milestone support

## 🔧 Development

```bash
# Install dependencies
npm install

# Start development server with auto-reload
npm run dev

# Start production server
npm start
```

## 🌐 Environment

The backend expects MongoDB to be running on:
- **Host**: localhost
- **Port**: 27017
- **Database**: financetracker

## 📝 Notes

- All API responses follow a consistent JSON format
- Error handling is implemented for all endpoints
- Database models include validation and virtual fields
- Routes are organized by feature for better maintainability
