const express = require('express')
const dbConnect = require('./dbConnect')
const errorHandler = require('./middleware/errorHandler')
const app = express()

// Middleware
app.use(express.json())

// Routes
const userRoute = require('./routes/usersRoute')
const transactionsRoute = require('./routes/transactionsRoute')
const budgetsRoute = require('./routes/budgetsRoute')
const goalsRoute = require('./routes/goalsRoute')

// API Routes
app.use('/api/users/', userRoute)
app.use('/api/transactions/', transactionsRoute)
app.use('/api/budgets/', budgetsRoute)
app.use('/api/goals/', goalsRoute)

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Finance Tracker API is running',
    timestamp: new Date().toISOString()
  })
})

// Error handling middleware (should be last)
app.use(errorHandler)

const port = process.env.PORT || 5000

app.listen(port, () => console.log(`Finance Tracker server started at port: ${port}`))