#!/usr/bin/env node

/**
 * Finance Tracker Server
 * Entry point for the application
 */

const app = require('./src/app');
const config = require('./src/config');

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Rejection:', err);
  process.exit(1);
});

// Start server
const server = app.listen(config.server.port, config.server.host, () => {
  console.log(`
🚀 Finance Tracker Server Started Successfully!
📍 Environment: ${config.server.env}
🌐 Server: http://${config.server.host}:${config.server.port}
📊 Health Check: http://${config.server.host}:${config.server.port}/health
📚 API Base: http://${config.server.host}:${config.server.port}${config.api.prefix}
⏰ Started at: ${new Date().toISOString()}
  `);
});

// Graceful shutdown
const gracefulShutdown = (signal) => {
  console.log(`\n${signal} received. Starting graceful shutdown...`);

  server.close((err) => {
    if (err) {
      console.error('Error during server shutdown:', err);
      process.exit(1);
    }

    console.log('Server closed successfully');
    process.exit(0);
  });

  // Force shutdown after 10 seconds
  setTimeout(() => {
    console.error('Forced shutdown after timeout');
    process.exit(1);
  }, 10000);
};

// Listen for shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));